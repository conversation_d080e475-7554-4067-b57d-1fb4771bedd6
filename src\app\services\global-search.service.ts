import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, of, timer } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, map, catchError } from 'rxjs/operators';
import { SearchResult, SearchIndex, SearchOptions, SearchResultType, SearchCategory } from '../core/models/search-result';
import { MetadataService } from './metadata.service';
import { SessionStorageService } from './session-storage.service';

@Injectable({
  providedIn: 'root'
})
export class GlobalSearchService {
  private metadataService = inject(MetadataService);
  private sessionStorage = inject(SessionStorageService);

  private searchIndex: SearchIndex[] = [];
  private indexedSubject = new BehaviorSubject<boolean>(false);
  private searchSubject = new BehaviorSubject<string>('');
  
  // Search configuration
  private readonly DEBOUNCE_TIME = 300;
  private readonly MIN_SEARCH_LENGTH = 2;
  private readonly MAX_RESULTS = 50;

  // Search categories for organizing results
  private readonly searchCategories: SearchCategory[] = [
    {
      name: 'Navigation',
      icon: 'menu',
      color: '#2196f3',
      types: [SearchResultType.MENU, SearchResultType.SUBMENU]
    },
    {
      name: 'Forms & Screens',
      icon: 'description',
      color: '#4caf50',
      types: [SearchResultType.FORM, SearchResultType.SCREEN, SearchResultType.TABLE]
    },
    {
      name: 'Queries & Reports',
      icon: 'query_stats',
      color: '#ff9800',
      types: [SearchResultType.QUERY]
    },
    {
      name: 'Fields & Actions',
      icon: 'settings',
      color: '#9c27b0',
      types: [SearchResultType.FIELD, SearchResultType.ACTION]
    }
  ];

  constructor() {
    this.initializeIndex();
  }

  /**
   * Initialize the search index with application data
   */
  private async initializeIndex(): Promise<void> {
    try {
      this.searchIndex = [];
      
      // Index user menus
      await this.indexUserMenus();
      
      // Index available forms and screens
      await this.indexFormsAndScreens();
      
      // Index common actions
      this.indexCommonActions();
      
      this.indexedSubject.next(true);
    } catch (error) {
      console.error('Error initializing search index:', error);
      this.indexedSubject.next(false);
    }
  }

  /**
   * Index user menus from session storage
   */
  private async indexUserMenus(): Promise<void> {
    const profile = this.sessionStorage.getUserProfile();
    if (!profile?.menus) {
      // Add some default search items if no menus are available
      this.addDefaultSearchItems();
      return;
    }

    for (const menu of profile.menus) {
      // Index main menu
      this.addToIndex({
        id: `menu-${menu.application}`,
        title: menu.description || menu.application,
        description: `${menu.type} - ${menu.application}`,
        keywords: [menu.application, menu.description, menu.type].filter(Boolean),
        type: this.getSearchResultType(menu.type),
        category: 'Navigation',
        icon: this.getMenuIcon(menu.type),
        metadata: menu,
        searchableText: `${menu.description || ''} ${menu.application || ''} ${menu.type || ''}`.toLowerCase()
      });

      // Index submenus if they exist
      if (menu.type === 'menu') {
        try {
          const submenuResponse = await this.metadataService.getMenu(this.extractApiId(menu.application)).toPromise();
          if (submenuResponse?.menus) {
            this.indexSubmenus(submenuResponse.menus, menu.application);
          }
        } catch (error) {
          // Silently continue if submenu loading fails
          console.warn('Failed to load submenu for:', menu.application, error);
        }
      }
    }
  }

  /**
   * Index submenus recursively
   */
  private indexSubmenus(submenus: any[], parentApplication: string): void {
    for (const submenu of submenus) {
      this.addToIndex({
        id: `submenu-${parentApplication}-${submenu.application}`,
        title: submenu.description || submenu.application,
        description: `${submenu.type} under ${parentApplication}`,
        keywords: [submenu.application, submenu.description, submenu.type, parentApplication].filter(Boolean),
        type: this.getSearchResultType(submenu.type),
        category: 'Navigation',
        icon: this.getMenuIcon(submenu.type),
        metadata: { ...submenu, parent: parentApplication },
        searchableText: `${submenu.description} ${submenu.application} ${submenu.type} ${parentApplication}`.toLowerCase()
      });
    }
  }

  /**
   * Index forms and screens (placeholder for future enhancement)
   */
  private async indexFormsAndScreens(): Promise<void> {
    // This could be enhanced to index known forms/screens
    // For now, we'll index based on menu items that are forms/screens
  }

  /**
   * Add default search items when no menus are available
   */
  private addDefaultSearchItems(): void {
    const defaultItems = [
      { name: 'Home', description: 'Go to home page', icon: 'home', type: SearchResultType.ACTION },
      { name: 'Dashboard', description: 'View dashboard', icon: 'dashboard', type: SearchResultType.ACTION },
      { name: 'Forms', description: 'Access forms', icon: 'description', type: SearchResultType.FORM },
      { name: 'Reports', description: 'View reports', icon: 'assessment', type: SearchResultType.QUERY }
    ];

    for (const item of defaultItems) {
      this.addToIndex({
        id: `default-${item.name.toLowerCase()}`,
        title: item.name,
        description: item.description,
        keywords: [item.name, item.description],
        type: item.type,
        category: 'Default',
        icon: item.icon,
        metadata: item,
        searchableText: `${item.name} ${item.description}`.toLowerCase()
      });
    }
  }

  /**
   * Index common actions
   */
  private indexCommonActions(): void {
    const commonActions = [
      { name: 'Logout', description: 'Sign out of the application', icon: 'logout' },
      { name: 'Profile', description: 'View user profile', icon: 'person' },
      { name: 'Settings', description: 'Application settings', icon: 'settings' },
      { name: 'Help', description: 'Get help and support', icon: 'help' }
    ];

    for (const action of commonActions) {
      this.addToIndex({
        id: `action-${action.name.toLowerCase()}`,
        title: action.name,
        description: action.description,
        keywords: [action.name, action.description],
        type: SearchResultType.ACTION,
        category: 'Actions',
        icon: action.icon,
        metadata: action,
        searchableText: `${action.name} ${action.description}`.toLowerCase()
      });
    }
  }

  /**
   * Add item to search index
   */
  private addToIndex(item: SearchIndex): void {
    this.searchIndex.push(item);
  }

  /**
   * Get search result type from menu type
   */
  private getSearchResultType(menuType: string): SearchResultType {
    switch (menuType) {
      case 'menu': return SearchResultType.MENU;
      case 'qur': return SearchResultType.QUERY;
      case 'scr': return SearchResultType.SCREEN;
      case 'table': return SearchResultType.TABLE;
      default: return SearchResultType.MENU;
    }
  }

  /**
   * Get icon for menu type
   */
  private getMenuIcon(menuType: string): string {
    switch (menuType) {
      case 'menu': return 'list';
      case 'qur': return 'query_stats';
      case 'scr': return 'screen_share';
      case 'table': return 'table_chart';
      default: return 'info';
    }
  }

  /**
   * Extract API ID from application string
   */
  private extractApiId(application: string): string {
    if (application && application.includes(',')) {
      return application.split(',')[0].trim();
    }
    return application;
  }

  /**
   * Search the index
   */
  search(query: string, options: SearchOptions = {}): Observable<SearchResult[]> {
    if (!query || query.length < this.MIN_SEARCH_LENGTH) {
      return of([]);
    }

    return this.indexedSubject.pipe(
      switchMap(indexed => {
        if (!indexed) {
          return of([]);
        }

        const results = this.performSearch(query, options);
        return of(results);
      })
    );
  }

  /**
   * Get search observable with debouncing
   */
  getSearchObservable(): Observable<SearchResult[]> {
    return this.searchSubject.pipe(
      debounceTime(this.DEBOUNCE_TIME),
      distinctUntilChanged(),
      switchMap(query => this.search(query))
    );
  }

  /**
   * Update search query
   */
  updateSearchQuery(query: string): void {
    this.searchSubject.next(query);
  }

  /**
   * Perform the actual search
   */
  private performSearch(query: string, options: SearchOptions): SearchResult[] {
    const searchTerm = query.toLowerCase().trim();
    const maxResults = options.maxResults || this.MAX_RESULTS;
    const minScore = options.minScore || 0.1;

    let results: SearchResult[] = [];

    for (const item of this.searchIndex) {
      // Skip if type is excluded
      if (options.excludeTypes?.includes(item.type)) continue;
      
      // Skip if type is not included (when includeTypes is specified)
      if (options.includeTypes && !options.includeTypes.includes(item.type)) continue;

      const score = this.calculateScore(item, searchTerm, options.fuzzySearch);
      
      if (score >= minScore) {
        results.push({
          id: item.id,
          title: item.title,
          description: item.description,
          type: item.type,
          category: item.category,
          icon: item.icon,
          route: item.route,
          metadata: item.metadata,
          score,
          highlightedTitle: this.highlightText(item.title, searchTerm),
          highlightedDescription: this.highlightText(item.description || '', searchTerm)
        });
      }
    }

    // Sort by score (descending) and limit results
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults);
  }

  /**
   * Calculate search score for an item
   */
  private calculateScore(item: SearchIndex, searchTerm: string, fuzzySearch = false): number {
    let score = 0;
    const title = item.title.toLowerCase();
    const description = (item.description || '').toLowerCase();
    const searchableText = item.searchableText;

    // Exact title match (highest score)
    if (title === searchTerm) {
      score += 100;
    }
    // Title starts with search term
    else if (title.startsWith(searchTerm)) {
      score += 80;
    }
    // Title contains search term
    else if (title.includes(searchTerm)) {
      score += 60;
    }

    // Description matches
    if (description.includes(searchTerm)) {
      score += 30;
    }

    // Keywords match
    for (const keyword of item.keywords) {
      const keywordLower = keyword.toLowerCase();
      if (keywordLower === searchTerm) {
        score += 50;
      } else if (keywordLower.includes(searchTerm)) {
        score += 20;
      }
    }

    // Fuzzy search in searchable text
    if (fuzzySearch && searchableText.includes(searchTerm)) {
      score += 10;
    }

    return score;
  }

  /**
   * Highlight search term in text
   */
  private highlightText(text: string, searchTerm: string): string {
    if (!text || !searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * Get search categories
   */
  getSearchCategories(): SearchCategory[] {
    return this.searchCategories;
  }

  /**
   * Refresh the search index
   */
  refreshIndex(): Promise<void> {
    return this.initializeIndex();
  }

  /**
   * Check if index is ready
   */
  isIndexReady(): Observable<boolean> {
    return this.indexedSubject.asObservable();
  }
}
