@use '../../../styles/shared.scss' as *;

.global-search-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  z-index: 1000;

  // Search Input Wrapper
  .search-input-wrapper {
    position: relative;
    width: 100%;

    .search-field {
      width: 100%;

      ::ng-deep {
        .mat-mdc-form-field-wrapper {
          padding: 0;
          background: transparent;
          border: none;
        }

        .mat-mdc-form-field-flex {
          background-color: rgba(0, 0, 0, 0.2) !important;
          border-radius: 24px !important;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          height: 40px;
          align-items: center;
          box-shadow: none !important;

          &:hover {
            background-color: rgba(0, 0, 0, 0.3) !important;
            border: 1px solid rgba(255, 255, 255, 0.5) !important;
          }

          &:focus-within {
            background-color: rgba(0, 0, 0, 0.4) !important;
            border: 1px solid rgba(255, 255, 255, 0.7) !important;
          }
        }

        .mat-mdc-form-field-outline,
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-end,
        .mat-mdc-form-field-outline-notch,
        .mat-mdc-form-field-outline-thick {
          display: none !important;
          border: none !important;
          background: transparent !important;
        }

        .mat-mdc-form-field-infix {
          padding: 0 12px;
          border: none;
          min-height: 38px;
          display: flex;
          align-items: center;

          input {
            color: white !important;
            caret-color: white !important;
            background-color: transparent !important;
            border: none !important;
            outline: none !important;
            height: 100%;
          }
        }

        .mat-mdc-form-field-subscript-wrapper {
          display: none !important;
        }

        // Additional Material Design overrides
        .mat-mdc-input-element {
          color: white !important;
          caret-color: white !important;
          background-color: transparent !important;

          &::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
          }

          &:focus {
            color: white !important;
            background-color: transparent !important;
          }
        }

        .mdc-text-field__input {
          color: white !important;
          caret-color: white !important;
          background-color: transparent !important;
        }

        .mat-mdc-form-field-focus-overlay {
          background-color: transparent !important;
        }

        // Force override any conflicting styles
        input[matInput] {
          color: white !important;
          background-color: transparent !important;
          caret-color: white !important;
          border: none !important;
          outline: none !important;
        }

        // Remove any Material Design container borders
        .mdc-text-field {
          border: none !important;
          background: transparent !important;
        }

        .mdc-text-field--outlined {
          border: none !important;
        }

        .mdc-notched-outline {
          display: none !important;
        }
      }

      .search-input {
        color: white !important;
        font-size: 14px;
        padding: 0;
        caret-color: white;
        border: none !important;
        outline: none !important;
        background: transparent !important;
        width: 100%;
        height: 100%;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7) !important;
        }

        &:focus {
          color: white !important;
          outline: none !important;
        }
      }

      .search-icon {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 8px;
      }

      .search-spinner {
        ::ng-deep circle {
          stroke: rgba(255, 255, 255, 0.7);
        }
      }

      .clear-button {
        color: rgba(255, 255, 255, 0.7);
        width: 32px;
        height: 32px;
        
        &:hover {
          color: white;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  // Search Results Container
  .search-results-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.08);
    overflow: hidden;
    animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 480px;
    display: flex;
    flex-direction: column;

    .search-results-wrapper {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .search-results-list {
      flex: 1;
      overflow-y: auto;
      max-height: 400px;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    // Search Result Item
    .search-result-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      border-bottom: 1px solid rgba(0, 0, 0, 0.04);

      &:last-child {
        border-bottom: none;
      }

      &:hover,
      &.selected {
        background-color: #f8f9fa;
        transform: translateX(2px);
      }

      &.selected {
        background-color: #e3f2fd;
        border-left: 3px solid var(--primary-color);
      }

      .result-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.04);
        margin-right: 12px;
        flex-shrink: 0;

        .result-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }

      .result-content {
        flex: 1;
        min-width: 0;

        .result-title {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
          line-height: 1.4;

          ::ng-deep mark {
            background-color: #fff3cd;
            color: #856404;
            padding: 1px 2px;
            border-radius: 2px;
            font-weight: 600;
          }
        }

        .result-description {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
          line-height: 1.3;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;

          ::ng-deep mark {
            background-color: #fff3cd;
            color: #856404;
            padding: 1px 2px;
            border-radius: 2px;
          }
        }

        .result-category {
          font-size: 11px;
          color: #999;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 500;
        }
      }

      .result-type-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        color: white;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        flex-shrink: 0;
        margin-left: 8px;
      }
    }

    // More Results Indicator
    .more-results-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 16px;
      background-color: #f8f9fa;
      color: #666;
      font-size: 12px;
      border-top: 1px solid rgba(0, 0, 0, 0.04);

      mat-icon {
        margin-right: 8px;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }

    // No Results Message
    .no-results-message {
      display: flex;
      align-items: center;
      padding: 24px 16px;
      text-align: center;
      color: #666;

      mat-icon {
        margin-right: 12px;
        font-size: 24px;
        width: 24px;
        height: 24px;
        color: #ccc;
      }

      .no-results-text {
        .no-results-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .no-results-subtitle {
          font-size: 12px;
          color: #999;
        }
      }
    }

    // Search Tips
    .search-tips {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #f8f9fa;
      color: #666;
      font-size: 12px;

      mat-icon {
        margin-right: 8px;
        font-size: 16px;
        width: 16px;
        height: 16px;
        color: #2196f3;
      }
    }

    // Index Loading
    .index-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px 16px;
      color: #666;
      font-size: 12px;

      mat-spinner {
        margin-right: 12px;
      }
    }

    // Search Footer
    .search-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.04);
      background-color: #fafafa;
      padding: 8px 16px;

      .search-shortcuts {
        display: flex;
        gap: 16px;
        font-size: 11px;
        color: #666;

        .shortcut {
          display: flex;
          align-items: center;
          gap: 4px;

          kbd {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            font-family: monospace;
            color: #495057;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .global-search-container {
    max-width: 100%;

    .search-results-container {
      left: -16px;
      right: -16px;
      margin-top: 4px;
      border-radius: 8px;
      max-height: 60vh;

      .search-result-item {
        padding: 16px;

        .result-icon-wrapper {
          width: 36px;
          height: 36px;
          margin-right: 16px;

          .result-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }

        .result-content {
          .result-title {
            font-size: 15px;
          }

          .result-description {
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .global-search-container {
    .search-input-wrapper {
      .search-field {
        ::ng-deep {
          .mat-mdc-form-field-flex {
            border-radius: 20px;
          }
        }

        .search-input {
          font-size: 16px; // Prevent zoom on iOS
        }
      }
    }

    .search-results-container {
      .search-footer {
        .search-shortcuts {
          flex-wrap: wrap;
          gap: 8px;
          font-size: 10px;
        }
      }
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .global-search-container {
    .search-results-container {
      background: #2d2d2d;
      border-color: #444;

      .search-result-item {
        border-bottom-color: #444;

        &:hover,
        &.selected {
          background-color: #3d3d3d;
        }

        &.selected {
          background-color: #1e3a8a;
        }

        .result-icon-wrapper {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .result-content {
          .result-title {
            color: #e5e5e5;
          }

          .result-description {
            color: #b5b5b5;
          }

          .result-category {
            color: #888;
          }
        }
      }

      .more-results-indicator,
      .search-tips {
        background-color: #3d3d3d;
        color: #b5b5b5;
      }

      .no-results-message {
        color: #b5b5b5;
      }

      .search-footer {
        background-color: #3d3d3d;
        border-top-color: #444;

        .search-shortcuts {
          color: #b5b5b5;

          kbd {
            background-color: #4d4d4d;
            border-color: #666;
            color: #e5e5e5;
          }
        }
      }
    }
  }
}
