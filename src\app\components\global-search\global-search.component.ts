import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, On<PERSON><PERSON>roy, <PERSON>ementRef, ViewChild, HostListener, inject, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil } from 'rxjs';
import { GlobalSearchService } from '../../services/global-search.service';
import { SearchResult, SearchResultType } from '../../core/models/search-result';
import { Router } from '@angular/router';

@Component({
  selector: 'app-global-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './global-search.component.html',
  styleUrls: ['./global-search.component.scss']
})
export class GlobalSearchComponent implements OnInit, OnDestroy {
  @ViewChild('searchInput', { static: true }) searchInput!: ElementRef<HTMLInputElement>;
  @ViewChild('resultsContainer') resultsContainer!: ElementRef<HTMLDivElement>;
  @Output() menuSelected = new EventEmitter<any>();

  private searchService = inject(GlobalSearchService);
  private router = inject(Router);
  private destroy$ = new Subject<void>();

  searchQuery = '';
  searchResults: SearchResult[] = [];
  isSearching = false;
  showResults = false;
  selectedIndex = -1;
  isIndexReady = false;

  // Search configuration
  readonly maxVisibleResults = 8;
  readonly placeholder = 'Search menus, forms, screens...';

  ngOnInit(): void {
    // Subscribe to search results
    this.searchService.getSearchObservable()
      .pipe(takeUntil(this.destroy$))
      .subscribe(results => {
        this.searchResults = results;
        this.isSearching = false;
        this.showResults = results.length > 0 && this.searchQuery.length >= 2;
        this.selectedIndex = -1;
      });

    // Check if search index is ready
    this.searchService.isIndexReady()
      .pipe(takeUntil(this.destroy$))
      .subscribe(ready => {
        this.isIndexReady = ready;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Handle search input changes
   */
  onSearchInput(): void {
    const query = this.searchQuery.trim();
    
    if (query.length >= 2) {
      this.isSearching = true;
      this.searchService.updateSearchQuery(query);
    } else {
      this.clearResults();
    }
  }

  /**
   * Handle keyboard navigation
   */
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.showResults) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.navigateResults(1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.navigateResults(-1);
        break;
      case 'Enter':
        event.preventDefault();
        this.selectResult();
        break;
      case 'Escape':
        event.preventDefault();
        this.clearResults();
        this.searchInput.nativeElement.blur();
        break;
    }
  }

  /**
   * Navigate through search results
   */
  private navigateResults(direction: number): void {
    const maxIndex = Math.min(this.searchResults.length - 1, this.maxVisibleResults - 1);
    
    if (direction > 0) {
      this.selectedIndex = this.selectedIndex < maxIndex ? this.selectedIndex + 1 : 0;
    } else {
      this.selectedIndex = this.selectedIndex > 0 ? this.selectedIndex - 1 : maxIndex;
    }

    // Scroll selected item into view
    this.scrollToSelectedItem();
  }

  /**
   * Scroll selected item into view
   */
  private scrollToSelectedItem(): void {
    if (this.resultsContainer && this.selectedIndex >= 0) {
      const selectedElement = this.resultsContainer.nativeElement
        .querySelector(`.search-result-item:nth-child(${this.selectedIndex + 1})`) as HTMLElement;
      
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }
  }

  /**
   * Select a search result
   */
  selectResult(index?: number): void {
    const resultIndex = index !== undefined ? index : this.selectedIndex;
    
    if (resultIndex >= 0 && resultIndex < this.searchResults.length) {
      const result = this.searchResults[resultIndex];
      this.handleResultSelection(result);
    }
  }

  /**
   * Handle result selection
   */
  private handleResultSelection(result: SearchResult): void {
    this.clearResults();
    this.searchQuery = '';

    // Handle different result types
    switch (result.type) {
      case SearchResultType.MENU:
      case SearchResultType.SUBMENU:
        this.handleMenuSelection(result);
        break;
      case SearchResultType.FORM:
      case SearchResultType.SCREEN:
      case SearchResultType.TABLE:
        this.handleFormSelection(result);
        break;
      case SearchResultType.QUERY:
        this.handleQuerySelection(result);
        break;
      case SearchResultType.ACTION:
        this.handleActionSelection(result);
        break;
      default:
        console.log('Selected result:', result);
    }
  }

  /**
   * Handle menu item selection
   */
  private handleMenuSelection(result: SearchResult): void {
    // Emit event to parent component (HomeComponent) to handle menu selection
    const menuItem = result.metadata;
    if (menuItem) {
      this.menuSelected.emit(menuItem);
    }
  }

  /**
   * Handle form/screen selection
   */
  private handleFormSelection(result: SearchResult): void {
    const metadata = result.metadata;
    if (metadata) {
      // Open the form/screen
      console.log('Form/Screen selected:', metadata);
      // This would integrate with your existing tab opening logic
    }
  }

  /**
   * Handle query selection
   */
  private handleQuerySelection(result: SearchResult): void {
    const metadata = result.metadata;
    if (metadata) {
      console.log('Query selected:', metadata);
      // This would integrate with your existing query opening logic
    }
  }

  /**
   * Handle action selection
   */
  private handleActionSelection(result: SearchResult): void {
    const action = result.metadata;
    
    switch (action.name.toLowerCase()) {
      case 'logout':
        // Handle logout
        console.log('Logout action triggered');
        break;
      case 'profile':
        // Handle profile view
        console.log('Profile action triggered');
        break;
      case 'settings':
        // Handle settings
        console.log('Settings action triggered');
        break;
      case 'help':
        // Handle help
        console.log('Help action triggered');
        break;
      default:
        console.log('Action selected:', action);
    }
  }

  /**
   * Clear search results
   */
  clearResults(): void {
    this.showResults = false;
    this.searchResults = [];
    this.selectedIndex = -1;
    this.isSearching = false;
  }

  /**
   * Handle input focus
   */
  onInputFocus(): void {
    if (this.searchQuery.length >= 2 && this.searchResults.length > 0) {
      this.showResults = true;
    }
  }

  /**
   * Handle input blur (with delay to allow for result clicks)
   */
  onInputBlur(): void {
    setTimeout(() => {
      this.showResults = false;
    }, 200);
  }

  /**
   * Get result icon
   */
  getResultIcon(result: SearchResult): string {
    return result.icon || 'info';
  }

  /**
   * Get result category color
   */
  getResultCategoryColor(result: SearchResult): string {
    const categories = this.searchService.getSearchCategories();
    const category = categories.find(cat => 
      cat.types.includes(result.type)
    );
    return category?.color || '#666';
  }

  /**
   * Get visible results (limited for performance)
   */
  getVisibleResults(): SearchResult[] {
    return this.searchResults.slice(0, this.maxVisibleResults);
  }

  /**
   * Check if there are more results
   */
  hasMoreResults(): boolean {
    return this.searchResults.length > this.maxVisibleResults;
  }

  /**
   * Get more results count
   */
  getMoreResultsCount(): number {
    return Math.max(0, this.searchResults.length - this.maxVisibleResults);
  }

  /**
   * Focus search input
   */
  focusSearch(): void {
    this.searchInput.nativeElement.focus();
  }

  /**
   * Clear search
   */
  clearSearch(): void {
    this.searchQuery = '';
    this.clearResults();
  }
}
