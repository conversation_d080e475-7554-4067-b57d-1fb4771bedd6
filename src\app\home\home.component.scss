@use '../../styles.scss' as *;

// Toolbar styling
.compact-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  min-height: 64px;

  .menu-icon {
    color: white;
  }

  .logo {
    height: 40px;
    width: auto;
  }

  .search-container {
    flex: 1;
    max-width: 400px;
    margin: 0 16px;
  }

  .spacer {
    flex: 1;
  }

  .icon-frame {
    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }

  .user-info {
    color: white;
    text-align: right;

    .username {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.2;
    }

    .company {
      font-size: 12px;
      opacity: 0.8;
      line-height: 1.2;
    }
  }
}

// Responsive design for toolbar
@media (max-width: 768px) {
  .compact-toolbar {
    padding: 0 8px;
    gap: 8px;

    .logo {
      height: 32px;
    }

    .search-container {
      max-width: 200px;
      margin: 0 8px;
    }

    .user-info {
      display: none; // Hide user info on mobile
    }
  }
}

@media (max-width: 480px) {
  .compact-toolbar {
    .search-container {
      max-width: 150px;
      margin: 0 4px;
    }

    .icon-frame {
      img {
        width: 28px;
        height: 28px;
      }
    }
  }
}

// Search result click highlight - shows exact location in sidebar
.compact-menu-item.search-clicked,
.compact-submenu-item.search-clicked {
  background-color: rgba(255, 193, 7, 0.3) !important;
  border-left: 4px solid #ffc107 !important;
  padding-left: 12px !important;
  transform: translateX(4px) !important;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3) !important;
  animation: searchClickPulse 1s ease-in-out !important;
  transition: all 0.3s ease !important;

  mat-icon {
    color: #ff8f00 !important;
    transform: scale(1.1) !important;
  }
}

// Search click pulse animation
@keyframes searchClickPulse {
  0% {
    background-color: rgba(255, 193, 7, 0.3);
    transform: translateX(4px) scale(1);
  }
  50% {
    background-color: rgba(255, 193, 7, 0.5);
    transform: translateX(6px) scale(1.02);
  }
  100% {
    background-color: rgba(255, 193, 7, 0.3);
    transform: translateX(4px) scale(1);
  }
}