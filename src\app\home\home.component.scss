@use '../../styles.scss' as *;

// Toolbar styling
.compact-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  min-height: 64px;

  .menu-icon {
    color: white;
  }

  .logo {
    height: 40px;
    width: auto;
  }

  .search-container {
    flex: 1;
    max-width: 400px;
    margin: 0 16px;
  }

  .spacer {
    flex: 1;
  }

  .icon-frame {
    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }

  .user-info {
    color: white;
    text-align: right;

    .username {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.2;
    }

    .company {
      font-size: 12px;
      opacity: 0.8;
      line-height: 1.2;
    }
  }
}

// Responsive design for toolbar
@media (max-width: 768px) {
  .compact-toolbar {
    padding: 0 8px;
    gap: 8px;

    .logo {
      height: 32px;
    }

    .search-container {
      max-width: 200px;
      margin: 0 8px;
    }

    .user-info {
      display: none; // Hide user info on mobile
    }
  }
}

@media (max-width: 480px) {
  .compact-toolbar {
    .search-container {
      max-width: 150px;
      margin: 0 4px;
    }

    .icon-frame {
      img {
        width: 28px;
        height: 28px;
      }
    }
  }
}

// Sidebar filter header
.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.15), rgba(33, 150, 243, 0.08));
  border-bottom: 2px solid rgba(33, 150, 243, 0.3);
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);

  .filter-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      color: #1976d2;
    }

    .filter-text {
      flex: 1;

      .filter-title {
        font-size: 13px;
        font-weight: 600;
        color: #1976d2;
        line-height: 1.2;
        margin-bottom: 2px;
      }

      .filter-subtitle {
        font-size: 11px;
        color: #1565c0;
        line-height: 1.2;
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .clear-filter-btn {
    width: 36px;
    height: 36px;
    color: #1976d2;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(33, 150, 243, 0.15);
      transform: scale(1.05);
    }

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

// Animation for filter header
.filter-header {
  animation: filterSlideIn 0.3s ease-out;
}

@keyframes filterSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

