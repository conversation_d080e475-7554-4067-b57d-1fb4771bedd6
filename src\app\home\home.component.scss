@use '../../styles.scss' as *;

// Toolbar styling
.compact-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  min-height: 64px;

  .menu-icon {
    color: white;
  }

  .logo {
    height: 40px;
    width: auto;
  }

  .search-container {
    flex: 1;
    max-width: 400px;
    margin: 0 16px;
  }

  .spacer {
    flex: 1;
  }

  .icon-frame {
    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }

  .user-info {
    color: white;
    text-align: right;

    .username {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.2;
    }

    .company {
      font-size: 12px;
      opacity: 0.8;
      line-height: 1.2;
    }
  }
}

// Responsive design for toolbar
@media (max-width: 768px) {
  .compact-toolbar {
    padding: 0 8px;
    gap: 8px;

    .logo {
      height: 32px;
    }

    .search-container {
      max-width: 200px;
      margin: 0 8px;
    }

    .user-info {
      display: none; // Hide user info on mobile
    }
  }
}

@media (max-width: 480px) {
  .compact-toolbar {
    .search-container {
      max-width: 150px;
      margin: 0 4px;
    }

    .icon-frame {
      img {
        width: 28px;
        height: 28px;
      }
    }
  }
}

// Search result highlighting in sidebar
.compact-menu-item,
.compact-submenu-item {
  transition: all 0.3s ease;
  position: relative;

  // Highlighted path (parent menus)
  &.highlighted-path {
    background-color: rgba(33, 150, 243, 0.1);
    border-left: 3px solid #2196f3;
    padding-left: 12px;
  }

  // Selected item from search
  &.selected-from-search {
    background-color: rgba(76, 175, 80, 0.2);
    border-left: 4px solid #4caf50;
    padding-left: 12px;
    font-weight: 600;
    color: #2e7d32;

    mat-icon {
      color: #4caf50;
    }
  }

  // Temporary highlight animation
  &.search-highlighted {
    animation: searchPulse 2s ease-in-out;
  }

  // Simulated hover effect (from search selection)
  &.simulated-hover {
    background-color: rgba(33, 150, 243, 0.15);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
    border-left: 3px solid #2196f3;
    transition: all 0.3s ease;

    mat-icon {
      color: #2196f3;
      transform: scale(1.1);
    }
  }

  // Simulated click effect (from search selection)
  &.simulated-click {
    background-color: rgba(76, 175, 80, 0.25);
    transform: translateX(6px) scale(1.02);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    border-left: 4px solid #4caf50;
    animation: clickPulse 0.6s ease;

    mat-icon {
      color: #4caf50;
      transform: scale(1.15);
    }
  }

  // Focus styles for keyboard navigation simulation
  &:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
    background-color: rgba(33, 150, 243, 0.1);
  }
}

// Search highlight animation
@keyframes searchPulse {
  0% {
    background-color: rgba(255, 193, 7, 0.3);
    transform: scale(1);
  }
  50% {
    background-color: rgba(255, 193, 7, 0.6);
    transform: scale(1.02);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}

// Click pulse animation
@keyframes clickPulse {
  0% {
    transform: translateX(6px) scale(1.02);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  }
  50% {
    transform: translateX(8px) scale(1.05);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
  }
  100% {
    transform: translateX(6px) scale(1.02);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  }
}

// Enhanced hover effects for highlighted items
.compact-menu-item.highlighted-path:hover,
.compact-submenu-item.highlighted-path:hover {
  background-color: rgba(33, 150, 243, 0.2);
}

.compact-menu-item.selected-from-search:hover,
.compact-submenu-item.selected-from-search:hover {
  background-color: rgba(76, 175, 80, 0.3);
}