<div class="global-search-container">
  <!-- Search Input -->
  <div class="search-input-wrapper">
    <mat-form-field appearance="outline" class="search-field">
      <mat-icon matPrefix class="search-icon">search</mat-icon>
      <input 
        matInput 
        #searchInput
        [(ngModel)]="searchQuery"
        [placeholder]="placeholder"
        (input)="onSearchInput()"
        (focus)="onInputFocus()"
        (blur)="onInputBlur()"
        autocomplete="off"
        class="search-input">
      
      <!-- Loading spinner -->
      @if (isSearching) {
        <mat-spinner matSuffix diameter="20" class="search-spinner"></mat-spinner>
      }
      
      <!-- Clear button -->
      @if (searchQuery && !isSearching) {
        <button
          matSuffix
          mat-icon-button
          (click)="clearSearch()"
          class="clear-button"
          type="button">
          <mat-icon>close</mat-icon>
        </button>
      }

      <!-- Debug refresh button (temporary) -->
      @if (!searchQuery && !isSearching) {
        <button
          matSuffix
          mat-icon-button
          (click)="refreshSearchIndex()"
          class="refresh-button"
          type="button"
          title="Refresh Search Index">
          <mat-icon>refresh</mat-icon>
        </button>
      }
    </mat-form-field>
  </div>

  <!-- Search Results Dropdown -->
  @if (showResults) {
    <div class="search-results-container" #resultsContainer>
      <div class="search-results-wrapper">
        <!-- Results List -->
        <div class="search-results-list">
          @for (result of getVisibleResults(); track result.id; let i = $index) {
            <div 
              class="search-result-item"
              [class.selected]="i === selectedIndex"
              (click)="selectResult(i)">
              
              <!-- Result Icon -->
              <div class="result-icon-wrapper">
                <mat-icon 
                  class="result-icon"
                  [style.color]="getResultCategoryColor(result)">
                  {{ getResultIcon(result) }}
                </mat-icon>
              </div>

              <!-- Result Content -->
              <div class="result-content">
                <div class="result-title" [innerHTML]="result.highlightedTitle || result.title"></div>
                @if (result.description) {
                  <div class="result-description" [innerHTML]="result.highlightedDescription || result.description"></div>
                }
                <div class="result-category">{{ result.category }}</div>
              </div>

              <!-- Result Type Badge -->
              <div class="result-type-badge" [style.background-color]="getResultCategoryColor(result)">
                {{ result.type }}
              </div>
            </div>
          }
        </div>

        <!-- More Results Indicator -->
        @if (hasMoreResults()) {
          <div class="more-results-indicator">
            <mat-icon>more_horiz</mat-icon>
            <span>{{ getMoreResultsCount() }} more results</span>
          </div>
        }

        <!-- No Results Message -->
        @if (searchResults.length === 0 && searchQuery.length >= 2 && !isSearching) {
          <div class="no-results-message">
            <mat-icon>search_off</mat-icon>
            <div class="no-results-text">
              <div class="no-results-title">No results found</div>
              <div class="no-results-subtitle">Try different keywords or check spelling</div>
            </div>
          </div>
        }

        <!-- Search Tips -->
        @if (searchQuery.length < 2 && searchQuery.length > 0) {
          <div class="search-tips">
            <mat-icon>info</mat-icon>
            <span>Type at least 2 characters to search</span>
          </div>
        }

        <!-- Index Loading -->
        @if (!isIndexReady) {
          <div class="index-loading">
            <mat-spinner diameter="24"></mat-spinner>
            <span>Preparing search index...</span>
          </div>
        }
      </div>

      <!-- Search Footer -->
      <div class="search-footer">
        <div class="search-shortcuts">
          <span class="shortcut">
            <kbd>↑</kbd><kbd>↓</kbd> Navigate
          </span>
          <span class="shortcut">
            <kbd>Enter</kbd> Select
          </span>
          <span class="shortcut">
            <kbd>Esc</kbd> Close
          </span>
        </div>
      </div>
    </div>
  }
</div>
