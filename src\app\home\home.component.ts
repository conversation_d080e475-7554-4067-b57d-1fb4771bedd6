import { Component, OnInit,
  ViewChild,
  ViewContainerRef,
  ComponentRef,
  ChangeDetectorRef,
  EnvironmentInjector,
  ElementRef,
  AfterViewInit,
  inject } from '@angular/core';
import { MatSidenavModule, MatSidenav } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { MetadataService } from '../services/metadata.service';
import { AuthenticationService } from '../services/authentication.service';
import { SessionStorageService } from '../services/session-storage.service';
import { NavigationService } from '../services/navigation.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SubmenuComponent } from '../submenu/submenu.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { DynamicFormComponent } from '../dynamic-form/dynamic-form.component';
import { DynamicQueryComponent } from '../dynamic-query/dynamic-query.component';
import { KeycloakService } from '../services/keycloak.service';
import { GlobalSearchComponent } from '../components/global-search/global-search.component';

@Component({
  selector: 'app-home',
  imports: [MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    SubmenuComponent,
    MatSidenav,
    GlobalSearchComponent],
     animations: [
    trigger('tabAnimation', [
      transition(':increment', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateX(20px)' }),
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
          ])
        ], { optional: true })
      ]),
      transition(':decrement', [
        query(':leave', [
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 0, transform: 'translateX(-20px)' }))
          ])
        ], { optional: true })
      ])
    ])
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit, AfterViewInit {
  tabs: any[] = [];
  sidebarMenus: { [key: string]: any[] } = {};
  tabMenus: { [key: string]: any[] } = {};
  loadingSidebarMenus: { [key: string]: boolean } = {};
  loadingTabMenus: { [key: string]: boolean } = {};
  expandedSidebarMenus: { [key: string]: boolean } = {};
  expandedTabMenus: { [key: string]: { [key: string]: boolean } } = {};
  selectedSidebarItem: any = null; // Track selected sidebar item
  highlightedMenuPath: string[] = []; // Track menu path for highlighting

  openTabs: any[] = [];
  activeTabIndex: number = -1;

  @ViewChild('sidenav') sidenav!: MatSidenav;
  isSidenavOpen: boolean = false;

  @ViewChild('tabContent', { read: ViewContainerRef }) tabContent!: ViewContainerRef;
  @ViewChild('tabScrollWrapper') tabScrollWrapper!: ElementRef;
  @ViewChild('tabsList') tabsList!: ElementRef;

  // Tab scrolling properties
  canScrollLeft: boolean = false;
  canScrollRight: boolean = false;


   userPrivileges: any[] = [];

  componentRefs: { [key: number]: ComponentRef<any> } = {}; // Store component references
  private environmentInjector = inject(EnvironmentInjector);

constructor(private metadataService: MetadataService,
    private authenticationService: AuthenticationService,
    private sessionStorage: SessionStorageService,
    private navigationService: NavigationService,
    private router: Router,
    private location: Location,
    private changeDetectorRef: ChangeDetectorRef,
    private keycloakService: KeycloakService,
){}
//  ngOnInit() {
//     const profile = JSON.parse(localStorage.getItem('profile') || '{}');
//     this.tabs = profile.menus || [];
//     console.log('Main Menu Loaded:', this.tabs);

//     this.location.subscribe(() => {
//       this.onLogout();
//     });
//   }

ngOnInit() {
  const profile = this.sessionStorage.getUserProfile() || {};
  this.tabs = profile.menus || [];

  this.userPrivileges = this.sessionStorage.getUserPrivileges();

  this.location.subscribe(() => {
    this.onLogout();
  });
}

selectBranch(branch: any): void {
  this.sessionStorage.setSelectedBranch(branch);
  window.location.reload();
}

  ngAfterViewInit() {
    // Check scroll state after view initialization
    setTimeout(() => {
      this.checkScrollState();
    }, 100);

    // Add resize observer to update scroll state when container size changes
    if (this.tabScrollWrapper) {
      const resizeObserver = new ResizeObserver(() => {
        this.checkScrollState();
      });
      resizeObserver.observe(this.tabScrollWrapper.nativeElement);
    }
  }

   onSidebarMenuItemSelected(menuItem: any) {
    if (menuItem.type === 'menu') {
      this.expandedSidebarMenus[menuItem.application] = !this.expandedSidebarMenus[menuItem.application];
      if (!this.sidebarMenus[menuItem.application]) {
        this.loadSidebarSubmenu(menuItem.application);
      }
    } else {
      this.openTab(menuItem);
    }

    // Reinitialize or reorganize layout
    setTimeout(() => {
      this.changeDetectorRef.detectChanges();
    }, 100); // Delay of 100ms
  }

  /**
   * Handle menu selection from global search
   */
  onSearchMenuSelected(menuItem: any) {
    console.log('Home Component - Received search menu selection:', menuItem);

    // Enhanced navigation: expand parent menus and navigate to position
    this.navigateToSearchResult(menuItem);
  }

  /**
   * Navigate to search result with full menu expansion and visual highlighting
   */
  private async navigateToSearchResult(menuItem: any) {
    try {
      console.log('Navigating to search result:', menuItem);

      // Step 1: Ensure sidebar is open to show the navigation
      if (!this.isSidenavOpen) {
        this.toggleSideMenu();
        // Wait for sidebar to open
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Step 2: Determine if this is a submenu item
      const isSubmenuItem = menuItem.parent || menuItem.application?.includes(',');

      if (isSubmenuItem) {
        // Step 3: Find and expand the parent menu
        const parentMenuName = menuItem.parent || this.extractParentFromApplication(menuItem.application);
        await this.expandParentMenu(parentMenuName);

        // Step 4: Wait for menu to load and expand
        await this.waitForMenuExpansion(parentMenuName);

        // Step 5: Highlight the menu path
        this.highlightMenuPath(parentMenuName, menuItem);
      } else {
        // For main menu items, just highlight them
        this.highlightMenuPath('', menuItem);
      }

      // Step 6: Set the selected item for visual highlighting
      this.selectedSidebarItem = menuItem;

      // Step 7: Force open the selected item (ensure it actually opens)
      this.onSidebarMenuItemSelected(menuItem);

      // Step 8: Scroll to the selected item in sidebar and keep it highlighted
      setTimeout(() => {
        this.scrollToSelectedMenuItem(menuItem);
        this.maintainSidebarSelection(menuItem);
      }, 800);

    } catch (error) {
      console.error('Error navigating to search result:', error);
      // Fallback to simple navigation
      this.onSidebarMenuItemSelected(menuItem);
    }
  }

  /**
   * Extract parent menu name from application string
   */
  private extractParentFromApplication(application: string): string {
    if (application && application.includes(',')) {
      // For items like "teller,LocalDeposit", parent is "tellerScreens"
      const baseName = application.split(',')[0];
      // Check if there's a corresponding menu with "Screens" suffix
      const parentWithScreens = baseName + 'Screens';
      const parentMenu = this.tabs.find(tab => tab.application === parentWithScreens);
      if (parentMenu) {
        return parentWithScreens;
      }
      // Otherwise, look for a menu that contains this item
      return this.findParentMenuForItem(application);
    }
    return '';
  }

  /**
   * Find parent menu that contains the given item
   */
  private findParentMenuForItem(application: string): string {
    // Look through all menu tabs to find which one might contain this item
    for (const tab of this.tabs) {
      if (tab.type === 'menu') {
        // Check if this menu might contain the item
        const baseName = application.split(',')[0];
        if (tab.application.toLowerCase().includes(baseName.toLowerCase()) ||
            baseName.toLowerCase().includes(tab.application.toLowerCase())) {
          return tab.application;
        }
      }
    }
    return '';
  }

  /**
   * Expand parent menu in sidebar
   */
  private async expandParentMenu(parentMenuName: string): Promise<void> {
    if (!parentMenuName) return;

    console.log('Expanding parent menu:', parentMenuName);

    // Find the parent menu tab
    const parentTab = this.tabs.find(tab => tab.application === parentMenuName);
    if (!parentTab) {
      console.warn('Parent menu not found:', parentMenuName);
      return;
    }

    // Expand the parent menu if not already expanded
    if (!this.expandedSidebarMenus[parentMenuName]) {
      this.expandedSidebarMenus[parentMenuName] = true;

      // Load submenu if not already loaded
      if (!this.sidebarMenus[parentMenuName]) {
        await this.loadSidebarSubmenuAsync(parentMenuName);
      }
    }
  }

  /**
   * Load sidebar submenu asynchronously
   */
  private loadSidebarSubmenuAsync(application: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.loadingSidebarMenus[application] = true;

      const apiId = this.extractApiId(application);

      this.metadataService.getMenu(apiId).subscribe({
        next: (response: any) => {
          this.sidebarMenus[application] = response?.menus || [];
          this.loadingSidebarMenus[application] = false;
          console.log('Loaded submenu for:', application, this.sidebarMenus[application]);
          resolve();
        },
        error: (error: any) => {
          this.loadingSidebarMenus[application] = false;
          console.error('Failed to load submenu:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * Wait for menu expansion to complete
   */
  private waitForMenuExpansion(menuName: string): Promise<void> {
    return new Promise((resolve) => {
      // Wait a bit for the menu to expand and load
      setTimeout(() => {
        this.changeDetectorRef.detectChanges();
        resolve();
      }, 300);
    });
  }

  /**
   * Highlight the menu path for visual feedback
   */
  private highlightMenuPath(parentMenuName: string, menuItem: any) {
    this.highlightedMenuPath = [];

    if (parentMenuName) {
      this.highlightedMenuPath.push(parentMenuName);
    }

    if (menuItem.application) {
      this.highlightedMenuPath.push(menuItem.application);
    }

    console.log('Highlighted menu path:', this.highlightedMenuPath);
  }

  /**
   * Scroll to selected menu item in sidebar
   */
  private scrollToSelectedMenuItem(menuItem: any) {
    try {
      // Find the menu item element and scroll to it
      const menuSelector = `[data-menu-item="${menuItem.application}"]`;
      const menuElement = document.querySelector(menuSelector);

      if (menuElement) {
        menuElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Add a temporary highlight effect
        menuElement.classList.add('search-highlighted');
        setTimeout(() => {
          menuElement.classList.remove('search-highlighted');
        }, 2000);
      }
    } catch (error) {
      console.warn('Could not scroll to menu item:', error);
    }
  }

  /**
   * Maintain sidebar selection state
   */
  private maintainSidebarSelection(menuItem: any) {
    // Keep the item selected and highlighted
    this.selectedSidebarItem = menuItem;

    // Force change detection to update the UI
    this.changeDetectorRef.detectChanges();

    console.log('Maintaining sidebar selection for:', menuItem);
  }
  

  openTab(menuItem: any) {
    let existingTabIndex = -1;
    const isQuery = menuItem.type === 'qur';

    if (isQuery) {
      existingTabIndex = this.openTabs.findIndex(t => t.type === 'qur');
    } else {
      existingTabIndex = this.openTabs.findIndex(
        (tab) => tab.application === menuItem.application && tab.type === menuItem.type
      );
    }

    if (existingTabIndex > -1) {
      // Tab exists
      this.activeTabIndex = existingTabIndex;
      const existingTab = this.openTabs[existingTabIndex];

      if (isQuery && existingTab.application !== menuItem.application) {
        // It's a different query, so we need to update the component
        // Destroy the old component
        if (this.componentRefs[existingTab.id]) {
          this.componentRefs[existingTab.id].destroy();
          delete this.componentRefs[existingTab.id];
        }
        // Update the tab data
        this.openTabs[existingTabIndex] = { ...menuItem, id: existingTab.id, data: {}, formState: null };
      }
    } else {
      // Create a new tab
      const newTab = { ...menuItem, id: Date.now(), data: {}, formState: null };
      this.openTabs.push(newTab);
      this.activeTabIndex = this.openTabs.length - 1;
    }

    // Load component for the active tab.
    // This will create it if it doesn't exist.
    if (this.activeTabIndex !== -1) {
      this.loadComponent(this.openTabs[this.activeTabIndex]);
    }

    // Check scroll state after tab changes
    setTimeout(() => {
      this.checkScrollState();
    }, 100);
  }
  
  loadComponent(tab: any) {
    // Ensure all other components are hidden before proceeding
    this.hideAllComponents();

    if (this.componentRefs[tab.id]) {
      // If the component already exists, show it
      this.showComponent(tab.id);
    } else {
      // Create the component and store its reference
      this.createComponent(tab);
    }
  
    // Trigger change detection to ensure the view updates
    this.changeDetectorRef.detectChanges();
  }

  // Helper function to extract API ID from application string
  private extractApiId(application: string): string {
    if (application && application.includes(',')) {
      return application.split(',')[0].trim();
    }
    return application;
  }

  createComponent(tab: any) {
    let componentRef: ComponentRef<any>;
    
    // Both 'table' and 'scr' types now use DynamicFormComponent
    switch (tab.type) {
      case 'table':
        componentRef = this.tabContent.createComponent(DynamicFormComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.tableName = tab.application;
        break;
      case 'scr':
        componentRef = this.tabContent.createComponent(DynamicFormComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.screenName = tab.application; // Use screenName for screen types
        break;
      case 'qur':
        componentRef = this.tabContent.createComponent(DynamicQueryComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.queryName = tab.application;
        break;
      default:
        return;
    }

    // Store the component reference and initialize data
    this.componentRefs[tab.id] = componentRef;
    componentRef.instance.data = tab.data;

    // Listen for data changes to keep the state updated
    componentRef.instance.dataChange.subscribe((updatedData: any) => {
      tab.data = updatedData;
    });

    // Hide all other components and show the new one
    this.showComponent(tab.id);
  }


  hideAllComponents() {
    for (const key in this.componentRefs) {
      if (this.componentRefs.hasOwnProperty(key)) {
        this.componentRefs[key].location.nativeElement.style.display = 'none';
      }
    }
  }

  showComponent(tabId: number) {
    // First, hide all components
    this.hideAllComponents();
  
    // Then, show the active one
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].location.nativeElement.style.display = 'block';
    }
  }
  

  setActiveTab(index: number) {
    this.activeTabIndex = index;
    this.showComponent(this.openTabs[index].id);
  }
  

  closeTab(index: number) {
    const tabId = this.openTabs[index].id;
    this.openTabs.splice(index, 1);

    // Destroy the component and remove its reference
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].destroy();
      delete this.componentRefs[tabId];
    }

    if (this.openTabs.length === 0) {
      this.activeTabIndex = -1;
    } else if (this.activeTabIndex >= this.openTabs.length) {
      this.activeTabIndex = this.openTabs.length - 1;
    }

    if (this.activeTabIndex !== -1) {
      this.showComponent(this.openTabs[this.activeTabIndex].id);
    }

    // Check scroll state after tab removal
    setTimeout(() => {
      this.checkScrollState();
    }, 100);
  }

  loadSidebarSubmenu(application: string) {
    this.loadingSidebarMenus[application] = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(application);

    this.metadataService.getMenu(apiId).subscribe({
      next: (response: any) => {
        this.sidebarMenus[application] = response?.menus || [];
        this.loadingSidebarMenus[application] = false;
      },
      error: (error: any) => {
        this.loadingSidebarMenus[application] = false;
      }
    });
  }

  loadTabSubmenu(application: string) {
    this.loadingTabMenus[application] = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(application);

    this.metadataService.getMenu(apiId).subscribe({
      next: (response: any) => {
        this.tabMenus[application] = response?.menus || [];
        this.loadingTabMenus[application] = false;
      },
      error: (error: any) => {
        this.loadingTabMenus[application] = false;
      }
    });
  }

  toggleSideMenu() {
    this.sidenav.toggle();
    this.isSidenavOpen = !this.isSidenavOpen;
  }

  getIcon(menuType: string): string {
    switch (menuType) {
      case 'menu':
        return 'list';
      case 'qur':
        return 'query_stats';
      case 'scr':
        return 'screen_share';
      case 'table':
        return 'table_chart';
      default:
        return 'info';
    }
  }

  // Tab scrolling methods
  checkScrollState() {
    if (this.tabScrollWrapper && this.tabsList) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const list = this.tabsList.nativeElement;

      this.canScrollLeft = wrapper.scrollLeft > 0;
      this.canScrollRight = wrapper.scrollLeft < (list.scrollWidth - wrapper.clientWidth);

      // Force change detection to update button states
      this.changeDetectorRef.detectChanges();
    } else {
    }
  }

  scrollTabsLeft() {
    if (this.tabScrollWrapper) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const scrollAmount = 150;

      wrapper.scrollTo({
        left: wrapper.scrollLeft - scrollAmount,
        behavior: 'smooth'
      });

      setTimeout(() => {
        this.checkScrollState();
      }, 300);
    } else {
    }
  }

  scrollTabsRight() {
    if (this.tabScrollWrapper) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const scrollAmount = 150;

      wrapper.scrollTo({
        left: wrapper.scrollLeft + scrollAmount,
        behavior: 'smooth'
      });

      setTimeout(() => {
        this.checkScrollState();
      }, 300);
    } else {
    }
  }

  // Add scroll event listener to update arrow states
  onTabScroll() {
    this.checkScrollState();
  }

  onLogout() {
    this.sessionStorage.clear();
    this.keycloakService.logout(); // Only Keycloak logout for SSO
  }

}
